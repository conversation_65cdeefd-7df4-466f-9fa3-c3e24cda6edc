package service

import (
	"context"
	"fmt"
	"math"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	repositoryErrors "pxpat-backend/pkg/errors/repository"
)

// FavoriteFolderService 收藏夹服务实现
type FavoriteFolderService struct {
	favoriteFolderRepo repository.FavoriteFolderRepository
	favoriteItemRepo   repository.FavoriteItemRepository
	userClient         client.UserServiceClient
}

// NewFavoriteFolderService 创建收藏夹服务实例
func NewFavoriteFolderService(
	favoriteFolderRepo repository.FavoriteFolderRepository,
	favoriteItemRepo repository.FavoriteItemRepository,
	userClient client.UserServiceClient,
) *FavoriteFolderService {
	return &FavoriteFolderService{
		favoriteFolderRepo: favoriteFolderRepo,
		favoriteItemRepo:   favoriteItemRepo,
		userClient:         userClient,
	}
}

// CreateFolder 创建收藏夹
func (s *FavoriteFolderService) CreateFolder(ctx context.Context, userKSUID string, req *dto.CreateFavoriteFolderRequest) (*dto.FavoriteFolderResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_name", req.DirName).
		Bool("is_public", req.IsPublic).
		Msg("Creating favorite folder")

	// 验证用户是否存在
	if err := s.validateUser(ctx, userKSUID); err != nil {
		return nil, err
	}

	// 检查用户收藏夹数量限制
	folders, err := s.favoriteFolderRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get user folders for limit check")
		return nil, fmt.Errorf("failed to check folder limit: %w", err)
	}

	// 设置默认限制
	maxFoldersPerUser := 50 // 默认最大收藏夹数量
	if len(folders) >= maxFoldersPerUser {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Int("current_count", len(folders)).
			Int("max_allowed", maxFoldersPerUser).
			Msg("User has reached maximum folder limit")
		return nil, repositoryErrors.ErrMaxFoldersExceeded
	}

	// 创建收藏夹模型
	folder := model.NewFavoriteFolder(userKSUID, req.DirName, req.Description, req.IsPublic)

	// 保存到数据库
	err = s.favoriteFolderRepo.Create(ctx, folder)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_name", req.DirName).
			Msg("Failed to create favorite folder")
		return nil, fmt.Errorf("failed to create folder: %w", err)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folder.FavoriteFolderID).
		Str("folder_name", req.DirName).
		Msg("Favorite folder created successfully")

	return s.toFolderResponse(folder), nil
}

// UpdateFolder 更新收藏夹
func (s *FavoriteFolderService) UpdateFolder(ctx context.Context, userKSUID, folderID string, req *dto.UpdateFavoriteFolderRequest) (*dto.FavoriteFolderResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Str("folder_name", req.DirName).
		Msg("Updating favorite folder")

	// 获取收藏夹
	folder, err := s.favoriteFolderRepo.GetByFolderKSUID(ctx, folderID)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if folder.UserKSUID != userKSUID {
		return nil, repositoryErrors.ErrUnauthorizedAccess
	}

	// 更新收藏夹信息
	folder.UpdateInfo(req.DirName, req.Description, req.IsPublic)

	// 保存更新
	err = s.favoriteFolderRepo.Update(ctx, folder)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Failed to update favorite folder")
		return nil, fmt.Errorf("failed to update folder: %w", err)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Str("folder_name", req.DirName).
		Msg("Favorite folder updated successfully")

	return s.toFolderResponse(folder), nil
}

// DeleteFolder 删除收藏夹
func (s *FavoriteFolderService) DeleteFolder(ctx context.Context, userKSUID, folderID string) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Msg("Deleting favorite folder")

	// 先获取收藏夹信息，检查是否可以删除
	folder, err := s.favoriteFolderRepo.GetByFolderKSUID(ctx, folderID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Failed to get favorite folder")
		if err == repositoryErrors.ErrFavoriteFolderNotFound {
			return errors.NewGlobalErrors(errors.RESOURCE_NOT_FOUND, errors.RECORD_NOT_FOUND, err)
		}
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 检查权限
	if folder.UserKSUID != userKSUID {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("folder_owner", folder.UserKSUID).
			Str("folder_id", folderID).
			Msg("User does not have permission to delete this folder")
		return errors.NewGlobalErrors(errors.PERMISSION_DENIED, errors.PERMISSION_DENIED, fmt.Errorf("permission denied"))
	}

	// 检查是否为默认收藏夹
	if folder.IsDefault {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Cannot delete default folder")
		return errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("cannot delete default folder"))
	}

	// 删除收藏夹中的所有收藏项
	if folder.ItemCount > 0 {
		log.Info().
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Int64("item_count", folder.ItemCount).
			Msg("Deleting favorite items in folder")

		err = s.favoriteItemRepo.DeleteByFolderID(ctx, userKSUID, folderID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("folder_id", folderID).
				Msg("Failed to delete favorite items in folder")
			return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
		}

		log.Info().
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Favorite items deleted successfully")
	}

	// 删除收藏夹
	err = s.favoriteFolderRepo.Delete(ctx, folderID, userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Failed to delete favorite folder")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Msg("Favorite folder deleted successfully")

	return nil
}

// GetFolder 获取单个收藏夹详情
func (s *FavoriteFolderService) GetFolder(ctx context.Context, currentUserKSUID, folderID string) (*dto.FavoriteFolderResponse, *errors.Errors) {
	log.Info().
		Str("current_user_ksuid", currentUserKSUID).
		Str("folder_id", folderID).
		Msg("Getting favorite folder details")

	// 获取收藏夹
	folder, err := s.favoriteFolderRepo.GetByFolderKSUID(ctx, folderID)
	if err != nil {
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 检查访问权限
	if folder.UserKSUID != currentUserKSUID && !folder.IsPublic {
		// 如果不是本人且收藏夹不是公开的，需要检查隐私设置
		return nil, errors.New(errors.PERMISSION_DENIED, "无权访问他人私有收藏夹")
	}

	return s.toFolderResponse(folder), nil
}

// GetFolders 获取用户的收藏夹列表
func (s *FavoriteFolderService) GetFolders(ctx context.Context, currentUserKSUID string, req *dto.GetFavoriteFoldersRequest) (*dto.GetFavoriteFoldersResponse, *errors.Errors) {
	log.Info().
		Str("current_user_ksuid", currentUserKSUID).
		Str("target_user_ksuid", req.UserKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("Getting user favorite folders")

	// 使用分页查询,不是本人则筛查公开收藏
	folders, total, err := s.favoriteFolderRepo.GetByUserKSUIDWithPaginationAndPublic(ctx, req.UserKSUID, currentUserKSUID != req.UserKSUID, req.Page, req.PageSize)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", req.UserKSUID).
			Msg("Failed to get user folders")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 如果不是本人访问，只返回公开的收藏夹
	if currentUserKSUID != req.UserKSUID {
		var publicFolders []*model.FavoriteFolder
		for _, folder := range folders {
			if folder.IsPublic {
				publicFolders = append(publicFolders, folder)
			}
		}
		folders = publicFolders
		total = int64(len(publicFolders))
	}

	// 转换为响应格式
	folderResponses := make([]*dto.FavoriteFolderResponse, 0)
	for _, folder := range folders {
		folderResponses = append(folderResponses, s.toFolderResponse(folder))
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	response := &dto.GetFavoriteFoldersResponse{
		Folders:    folderResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Int64("total", total).
		Int("returned", len(folderResponses)).
		Msg("User favorite folders retrieved successfully")

	return response, nil
}

// GetFavoriteStats 获取收藏统计信息
func (s *FavoriteFolderService) GetFavoriteStats(ctx context.Context, userKSUID string) (*dto.GetFavoriteStatsResponse, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Msg("Getting favorite stats")

	// 获取收藏夹统计信息
	folderStats, err := s.favoriteFolderRepo.GetUserFolderStats(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get favorite stats")
		return nil, fmt.Errorf("failed to get stats: %w", err)
	}

	// 直接返回Repository的结果，因为Repository已经返回了正确的结构
	return folderStats, nil
}

// ===== 辅助方法 =====

// validateUser 验证用户是否存在
func (s *FavoriteFolderService) validateUser(ctx context.Context, userKSUID string) error {
	// 这里应该调用用户服务验证用户
	// 暂时跳过验证，后续实现
	return nil
}

// checkPrivacyAccess 检查隐私访问权限
func (s *FavoriteFolderService) checkPrivacyAccess(ctx context.Context, currentUserKSUID, targetUserKSUID string) error {
	// 这里应该调用用户服务检查隐私设置
	// 暂时允许访问，后续实现
	return nil
}

// toFolderResponse 转换收藏夹模型为响应格式
func (s *FavoriteFolderService) toFolderResponse(folder *model.FavoriteFolder) *dto.FavoriteFolderResponse {
	return &dto.FavoriteFolderResponse{
		FavoriteFolderID: folder.FavoriteFolderID,
		UserKSUID:        folder.UserKSUID,
		DirName:          folder.DirName,
		Description:      folder.Description,
		IsPublic:         folder.IsPublic,
		SortOrder:        folder.SortOrder,
		IsDefault:        folder.IsDefault,
		ItemCount:        folder.ItemCount,
		CreatedAt:        folder.CreatedAt,
		UpdatedAt:        folder.UpdatedAt,
	}
}
