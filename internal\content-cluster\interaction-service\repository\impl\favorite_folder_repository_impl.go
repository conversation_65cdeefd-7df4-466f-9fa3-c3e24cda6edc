package impl

import (
	"context"
	"errors"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	repositoryErrors "pxpat-backend/pkg/errors/repository"
)

// favoriteFolderRepositoryImpl 收藏夹仓储实现
type favoriteFolderRepositoryImpl struct {
	db *gorm.DB
}

// NewFavoriteFolderRepository 创建收藏夹仓储实例
func NewFavoriteFolderRepository(db *gorm.DB) repository.FavoriteFolderRepository {
	return &favoriteFolderRepositoryImpl{db: db}
}

// Create 创建收藏夹
func (r *favoriteFolderRepositoryImpl) Create(ctx context.Context, folder *model.FavoriteFolder) error {
	// 检查收藏夹名称是否已存在
	exists, err := r.CheckFolderNameExists(ctx, folder.UserKSUID, folder.DirName, "")
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folder.UserKSUID).
			Str("folder_name", folder.DirName).
			Msg("Failed to check folder name existence")
		return err
	}
	if exists {
		return repositoryErrors.ErrFolderNameExists
	}

	result := r.db.WithContext(ctx).Create(folder)
	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", folder.UserKSUID).
			Str("folder_name", folder.DirName).
			Msg("Failed to create favorite folder")
		return result.Error
	}

	log.Info().
		Str("folder_id", folder.FavoriteFolderID).
		Str("user_ksuid", folder.UserKSUID).
		Str("folder_name", folder.DirName).
		Msg("Favorite folder created successfully")

	return nil
}

// Update 更新收藏夹信息
func (r *favoriteFolderRepositoryImpl) Update(ctx context.Context, folder *model.FavoriteFolder) error {
	// 检查收藏夹名称是否已存在（排除当前收藏夹）
	exists, err := r.CheckFolderNameExists(ctx, folder.UserKSUID, folder.DirName, folder.FavoriteFolderID)
	if err != nil {
		log.Error().Err(err).
			Str("folder_id", folder.FavoriteFolderID).
			Str("folder_name", folder.DirName).
			Msg("Failed to check folder name existence")
		return err
	}
	if exists {
		return repositoryErrors.ErrFolderNameExists
	}

	result := r.db.WithContext(ctx).Save(folder)
	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("folder_id", folder.FavoriteFolderID).
			Msg("Failed to update favorite folder")
		return result.Error
	}

	if result.RowsAffected == 0 {
		log.Warn().
			Str("folder_id", folder.FavoriteFolderID).
			Msg("No rows affected when updating favorite folder")
		return repositoryErrors.ErrFavoriteFolderNotFound
	}

	log.Info().
		Str("folder_id", folder.FavoriteFolderID).
		Str("folder_name", folder.DirName).
		Msg("Favorite folder updated successfully")

	return nil
}

// Delete 删除收藏夹
func (r *favoriteFolderRepositoryImpl) Delete(ctx context.Context, folderID string, userKSUID string) error {
	result := r.db.WithContext(ctx).Delete(&model.FavoriteFolder{}, "favorite_folder_id = ? AND user_ksuid = ?", folderID, userKSUID)
	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("folder_id", folderID).
			Str("user_ksuid", userKSUID).
			Msg("Failed to delete favorite folder")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repositoryErrors.ErrFavoriteFolderNotFound
	}

	log.Info().
		Str("folder_id", folderID).
		Str("user_ksuid", userKSUID).
		Msg("Favorite folder deleted successfully")

	return nil
}

// GetByFolderKSUID 根据收藏夹ID获取收藏夹
func (r *favoriteFolderRepositoryImpl) GetByFolderKSUID(ctx context.Context, folderID string) (*model.FavoriteFolder, error) {
	var folder model.FavoriteFolder
	result := r.db.WithContext(ctx).
		Where("favorite_folder_id = ?", folderID).
		First(&folder)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, repositoryErrors.ErrFavoriteFolderNotFound
		}
		log.Error().Err(result.Error).
			Str("folder_id", folderID).
			Msg("Failed to get favorite folder by ID")
		return nil, result.Error
	}

	return &folder, nil
}

// GetByUserKSUID 获取用户的所有收藏夹
func (r *favoriteFolderRepositoryImpl) GetByUserKSUID(ctx context.Context, userKSUID string) ([]*model.FavoriteFolder, error) {
	var folders []*model.FavoriteFolder
	result := r.db.WithContext(ctx).
		Where("user_ksuid = ?", userKSUID).
		Order("is_default DESC, sort_order ASC, created_at ASC").
		Find(&folders)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get user favorite folders")
		return nil, result.Error
	}

	return folders, nil
}

// GetByUserKSUIDWithPaginationAndPublic 分页获取用户的收藏夹
func (r *favoriteFolderRepositoryImpl) GetByUserKSUIDWithPaginationAndPublic(ctx context.Context, userKSUID string, isPublic bool, page, pageSize int) ([]*model.FavoriteFolder, int64, error) {
	var folders []*model.FavoriteFolder
	var total int64

	// 计算总数
	countResult := r.db.WithContext(ctx).
		Model(&model.FavoriteFolder{}).
		Where("user_ksuid = ?", userKSUID).
		Where("is_public = ?", isPublic).
		Count(&total)

	if countResult.Error != nil {
		log.Error().Err(countResult.Error).
			Str("user_ksuid", userKSUID).
			Msg("Failed to count user favorite folders")
		return nil, 0, countResult.Error
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := r.db.WithContext(ctx).
		Where("user_ksuid = ?", userKSUID).
		Where("is_public = ?", isPublic).
		Order("is_default DESC, sort_order ASC, created_at ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&folders)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Msg("Failed to get user favorite folders with pagination")
		return nil, 0, result.Error
	}

	return folders, total, nil
}

// GetDefaultFolder 获取用户的默认收藏夹
func (r *favoriteFolderRepositoryImpl) GetDefaultFolder(ctx context.Context, userKSUID string) (*model.FavoriteFolder, error) {
	var folder model.FavoriteFolder
	result := r.db.WithContext(ctx).
		Where("user_ksuid = ? AND is_default = ?", userKSUID, true).
		First(&folder)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, repositoryErrors.ErrFavoriteFolderNotFound
		}
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get default favorite folder")
		return nil, result.Error
	}

	return &folder, nil
}

// UpdateItemCount 更新收藏夹的收藏项数量
func (r *favoriteFolderRepositoryImpl) UpdateItemCount(ctx context.Context, folderID string, increment bool) error {
	var updateExpr string
	if increment {
		updateExpr = "item_count + 1"
	} else {
		updateExpr = "CASE WHEN item_count > 0 THEN item_count - 1 ELSE 0 END"
	}

	result := r.db.WithContext(ctx).
		Model(&model.FavoriteFolder{}).
		Where("favorite_folder_id = ?", folderID).
		Update("item_count", gorm.Expr(updateExpr))

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("folder_id", folderID).
			Bool("increment", increment).
			Msg("Failed to update folder item count")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return repositoryErrors.ErrFavoriteFolderNotFound
	}

	return nil
}

// CheckFolderNameExists 检查收藏夹名称是否已存在
func (r *favoriteFolderRepositoryImpl) CheckFolderNameExists(ctx context.Context, userKSUID, folderName string, excludeFolderID string) (bool, error) {
	query := r.db.WithContext(ctx).
		Model(&model.FavoriteFolder{}).
		Where("user_ksuid = ? AND dir_name = ?", userKSUID, folderName)

	if excludeFolderID != "" {
		query = query.Where("favorite_folder_id != ?", excludeFolderID)
	}

	var count int64
	result := query.Count(&count)
	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("folder_name", folderName).
			Msg("Failed to check folder name existence")
		return false, result.Error
	}

	return count > 0, nil
}

// UpdateSortOrder 更新收藏夹排序
func (r *favoriteFolderRepositoryImpl) UpdateSortOrder(ctx context.Context, userKSUID string, folderOrders map[string]int) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for folderID, sortOrder := range folderOrders {
			result := tx.Model(&model.FavoriteFolder{}).
				Where("favorite_folder_id = ? AND user_ksuid = ?", folderID, userKSUID).
				Update("sort_order", sortOrder)

			if result.Error != nil {
				log.Error().Err(result.Error).
					Str("folder_id", folderID).
					Str("user_ksuid", userKSUID).
					Int("sort_order", sortOrder).
					Msg("Failed to update folder sort order")
				return result.Error
			}
		}
		return nil
	})
}

// GetUserFolderStats 获取用户收藏夹统计信息
func (r *favoriteFolderRepositoryImpl) GetUserFolderStats(ctx context.Context, userKSUID string) (*dto.GetFavoriteStatsResponse, error) {
	var stats dto.FavoriteStats

	// 统计总收藏夹数
	result := r.db.WithContext(ctx).
		Model(&model.FavoriteFolder{}).
		Where("user_ksuid = ?", userKSUID).
		Count(&stats.TotalFolders)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Msg("Failed to count user folders")
		return nil, result.Error
	}

	// 统计公开收藏夹数
	result = r.db.WithContext(ctx).
		Model(&model.FavoriteFolder{}).
		Where("user_ksuid = ? AND is_public = ?", userKSUID, true).
		Count(&stats.PublicFolders)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Msg("Failed to count user public folders")
		return nil, result.Error
	}

	// 统计总收藏项数（所有收藏夹的item_count之和）
	var totalItems int64
	result = r.db.WithContext(ctx).
		Model(&model.FavoriteFolder{}).
		Where("user_ksuid = ?", userKSUID).
		Select("COALESCE(SUM(item_count), 0)").
		Scan(&totalItems)

	if result.Error != nil {
		log.Error().Err(result.Error).
			Str("user_ksuid", userKSUID).
			Msg("Failed to sum user folder item counts")
		return nil, result.Error
	}

	stats.TotalItems = totalItems

	return &dto.GetFavoriteStatsResponse{
		Stats: &stats,
	}, nil
}
