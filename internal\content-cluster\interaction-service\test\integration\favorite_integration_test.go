package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/handler"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository/impl"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

// Mock UserServiceClient for integration tests
type MockUserServiceClient struct{}

func (m *MockUserServiceClient) GetUserByKSUID(userKSUID string) (*client.UserInfo, error) {
	return &client.UserInfo{
		UserKSUID: userKSUID,
		Username:  "test_user",
		Email:     "<EMAIL>",
	}, nil
}

func (m *MockUserServiceClient) CheckUserExists(userKSUID string) (bool, error) {
	return true, nil
}

func (m *MockUserServiceClient) GetUserPrivacySettings(userKSUID string) (*client.UserPrivacySettings, error) {
	return &client.UserPrivacySettings{
		FavoritePrivacy: "public",
	}, nil
}

// Mock VideoServiceClient for integration tests
type MockVideoServiceClient struct{}

func (m *MockVideoServiceClient) GetContentByKSUID(contentKSUID string) (*client.ContentInfo, error) {
	return &client.ContentInfo{
		ContentKSUID: contentKSUID,
		Title:        "Test Content",
		Type:         "video",
		Status:       "published",
	}, nil
}

func (m *MockVideoServiceClient) UpdateContentFavoriteCount(contentKSUID string, increment bool) error {
	return nil
}

func (m *MockVideoServiceClient) BatchGetContentByKSUIDs(contentKSUIDs []string) (map[string]*client.ContentInfo, error) {
	result := make(map[string]*client.ContentInfo)
	for _, ksuid := range contentKSUIDs {
		result[ksuid] = &client.ContentInfo{
			ContentKSUID: ksuid,
			Title:        "Test Content " + ksuid,
			Type:         "video",
			Status:       "published",
		}
	}
	return result, nil
}

// Integration Test Suite
type FavoriteIntegrationTestSuite struct {
	suite.Suite
	db                    *gorm.DB
	router                *gin.Engine
	favoriteFolderHandler *handler.FavoriteFolderHandler
	favoriteItemHandler   *handler.FavoriteItemHandler
	ctx                   context.Context
}

func (suite *FavoriteIntegrationTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
	suite.ctx = context.Background()

	// 设置内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 自动迁移表结构
	err = db.AutoMigrate(&model.FavoriteFolder{})
	suite.Require().NoError(err)

	// 创建收藏项分表
	for i := 0; i < 16; i++ {
		tableName := fmt.Sprintf("interaction_favorite_items_%d", i)
		err = db.Table(tableName).AutoMigrate(&model.FavoriteItem{})
		suite.Require().NoError(err)
	}

	suite.db = db

	// 创建Repository层
	folderRepo := impl.NewFavoriteFolderRepository(db)
	itemRepo := impl.NewFavoriteItemRepository(db)

	// 创建Mock客户端
	userClient := &MockUserServiceClient{}
	videoClient := &MockVideoServiceClient{}

	// 创建Service层
	folderService := service.NewFavoriteFolderService(folderRepo, itemRepo, userClient)
	itemService := service.NewFavoriteItemService(itemRepo, userClient, videoClient)

	// 创建Handler层
	suite.favoriteFolderHandler = handler.NewFavoriteFolderHandler(folderService)
	suite.favoriteItemHandler = handler.NewFavoriteItemHandler(itemService)

	// 设置路由
	suite.setupRoutes()
}

func (suite *FavoriteIntegrationTestSuite) setupRoutes() {
	suite.router = gin.New()

	// 设置测试用户KSUID中间件
	suite.router.Use(func(c *gin.Context) {
		userKSUID := c.GetHeader("X-User-KSUID")
		if userKSUID == "" {
			userKSUID = "test_user_ksuid"
		}
		c.Set("user_ksuid", userKSUID)
		c.Next()
	})

	api := suite.router.Group("/api/v1")
	{
		favorites := api.Group("/favorites")
		{
			// 收藏夹路由
			folders := favorites.Group("/folders")
			{
				folders.POST("", suite.favoriteFolderHandler.CreateFolder)
				folders.PUT("/:folder_id", suite.favoriteFolderHandler.UpdateFolder)
				folders.DELETE("/:folder_id", suite.favoriteFolderHandler.DeleteFolder)
				folders.GET("", suite.favoriteFolderHandler.GetFolders)
				folders.GET("/:folder_id", suite.favoriteFolderHandler.GetFolder)
			}

			// 收藏项路由
			items := favorites.Group("/items")
			{
				items.POST("", suite.favoriteItemHandler.AddToFavorite)
				items.DELETE("", suite.favoriteItemHandler.RemoveFromFavorite)
				items.GET("", suite.favoriteItemHandler.GetFavoriteItems)
				items.GET("/status", suite.favoriteItemHandler.CheckFavoriteStatus)
			}

			// 统计路由
			favorites.GET("/stats", suite.favoriteFolderHandler.GetFavoriteStats)
		}
	}
}

func (suite *FavoriteIntegrationTestSuite) TearDownTest() {
	// 清理测试数据
	suite.db.Exec("DELETE FROM interaction_favorite_folders")
	for i := 0; i < 16; i++ {
		tableName := fmt.Sprintf("interaction_favorite_items_%d", i)
		suite.db.Exec("DELETE FROM " + tableName)
	}
}

func (suite *FavoriteIntegrationTestSuite) TestCompleteWorkflow() {
	userKSUID := ksuid.GenerateKSUID()

	// 1. 创建收藏夹
	createFolderReq := &dto.CreateFolderRequest{
		DirName:     "我的收藏",
		Description: "测试收藏夹",
		IsPublic:    false,
	}

	folderID := suite.createFolder(userKSUID, createFolderReq)
	assert.NotEmpty(suite.T(), folderID)

	// 2. 获取收藏夹列表
	folders := suite.getFolders(userKSUID)
	assert.Len(suite.T(), folders, 1)
	assert.Equal(suite.T(), createFolderReq.DirName, folders[0]["dir_name"])

	// 3. 添加收藏项
	contentKSUID := ksuid.GenerateKSUID()
	addToFavoriteReq := &dto.AddToFavoriteRequest{
		ContentKSUID:      contentKSUID,
		ContentType:       "video",
		FavoriteFolderIDs: []string{folderID},
	}

	suite.addToFavorite(userKSUID, addToFavoriteReq)

	// 4. 检查收藏状态
	status := suite.checkFavoriteStatus(userKSUID, contentKSUID)
	assert.True(suite.T(), status["is_favorited"].(bool))

	// 5. 获取收藏项列表
	items := suite.getFavoriteItems(userKSUID, folderID)
	assert.Len(suite.T(), items, 1)
	assert.Equal(suite.T(), contentKSUID, items[0]["content_ksuid"])

	// 6. 获取收藏统计
	stats := suite.getFavoriteStats(userKSUID)
	assert.Equal(suite.T(), float64(1), stats["total_folders"])
	assert.Equal(suite.T(), float64(1), stats["total_items"])

	// 7. 移除收藏项
	removeFromFavoriteReq := &dto.RemoveFromFavoriteRequest{
		ContentKSUID:      contentKSUID,
		FavoriteFolderIDs: []string{folderID},
	}

	suite.removeFromFavorite(userKSUID, removeFromFavoriteReq)

	// 8. 验证收藏项已被移除
	status = suite.checkFavoriteStatus(userKSUID, contentKSUID)
	assert.False(suite.T(), status["is_favorited"].(bool))

	// 9. 删除收藏夹
	suite.deleteFolder(userKSUID, folderID)

	// 10. 验证收藏夹已被删除
	folders = suite.getFolders(userKSUID)
	assert.Len(suite.T(), folders, 0)
}

// Helper methods for making HTTP requests
func (suite *FavoriteIntegrationTestSuite) createFolder(userKSUID string, req *dto.CreateFolderRequest) string {
	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/v1/favorites/folders", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	data := response.Data.(map[string]interface{})
	return data["favorite_folder_id"].(string)
}

func (suite *FavoriteIntegrationTestSuite) getFolders(userKSUID string) []map[string]interface{} {
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/favorites/folders", nil)
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	data := response.Data.(map[string]interface{})
	folders := data["folders"].([]interface{})

	result := make([]map[string]interface{}, len(folders))
	for i, folder := range folders {
		result[i] = folder.(map[string]interface{})
	}
	return result
}

func (suite *FavoriteIntegrationTestSuite) addToFavorite(userKSUID string, req *dto.AddToFavoriteRequest) {
	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/v1/favorites/items", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *FavoriteIntegrationTestSuite) checkFavoriteStatus(userKSUID, contentKSUID string) map[string]interface{} {
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/favorites/items/status?content_ksuid="+contentKSUID, nil)
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	return response.Data.(map[string]interface{})
}

func (suite *FavoriteIntegrationTestSuite) getFavoriteItems(userKSUID, folderID string) []map[string]interface{} {
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/favorites/items?favorite_folder_id="+folderID, nil)
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	data := response.Data.(map[string]interface{})
	items := data["items"].([]interface{})

	result := make([]map[string]interface{}, len(items))
	for i, item := range items {
		result[i] = item.(map[string]interface{})
	}
	return result
}

func (suite *FavoriteIntegrationTestSuite) getFavoriteStats(userKSUID string) map[string]interface{} {
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/favorites/stats", nil)
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response globalTypes.GlobalResponse
	json.Unmarshal(w.Body.Bytes(), &response)
	return response.Data.(map[string]interface{})
}

func (suite *FavoriteIntegrationTestSuite) removeFromFavorite(userKSUID string, req *dto.RemoveFromFavoriteRequest) {
	reqBody, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("DELETE", "/api/v1/favorites/items", bytes.NewBuffer(reqBody))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *FavoriteIntegrationTestSuite) deleteFolder(userKSUID, folderID string) {
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("DELETE", "/api/v1/favorites/folders/"+folderID, nil)
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *FavoriteIntegrationTestSuite) TestDeleteFolderWithItems() {
	userKSUID := "test_user_delete_folder"

	// 创建收藏夹
	folderResp := suite.createFolder(userKSUID, &dto.CreateFolderRequest{
		DirName:     "Test Folder For Deletion",
		Description: "Test Description",
		IsPublic:    true,
	})
	folderID := folderResp["favorite_folder_id"].(string)

	// 添加多个内容到收藏夹
	for i := 1; i <= 3; i++ {
		contentKSUID := fmt.Sprintf("test_content_delete_%d", i)
		suite.addToFavorite(userKSUID, &dto.AddToFavoriteRequest{
			ContentKSUID:      contentKSUID,
			ContentType:       "video",
			FavoriteFolderIDs: []string{folderID},
		})
	}

	// 验证内容已添加
	items := suite.getFavoriteItems(userKSUID, folderID)
	suite.Require().Equal(3, len(items))

	// 获取收藏夹列表，确认收藏夹存在
	folders := suite.getFolders(userKSUID)
	foundFolder := false
	for _, folder := range folders {
		if folder["favorite_folder_id"] == folderID {
			foundFolder = true
			suite.Equal(int64(3), int64(folder["item_count"].(float64)))
			break
		}
	}
	suite.True(foundFolder, "收藏夹应该存在于列表中")

	// 删除收藏夹
	suite.deleteFolder(userKSUID, folderID)

	// 验证收藏夹已删除
	folders = suite.getFolders(userKSUID)
	foundFolder = false
	for _, folder := range folders {
		if folder["favorite_folder_id"] == folderID {
			foundFolder = true
			break
		}
	}
	suite.False(foundFolder, "收藏夹应该已被删除")

	// 尝试获取已删除收藏夹中的内容，应该返回空列表或错误
	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/favorites/items?favorite_folder_id="+folderID, nil)
	httpReq.Header.Set("X-User-KSUID", userKSUID)

	suite.router.ServeHTTP(w, httpReq)
	// 可能返回404或空列表，取决于API设计
	if w.Code == http.StatusOK {
		var response globalTypes.GlobalResponse
		json.Unmarshal(w.Body.Bytes(), &response)
		data := response.Data.(map[string]interface{})
		items := data["items"].([]interface{})
		suite.Equal(0, len(items), "删除收藏夹后，其中的收藏项应该也被删除")
	} else {
		suite.Equal(http.StatusNotFound, w.Code, "删除收藏夹后，尝试访问其中的收藏项应该返回404")
	}
}

func TestFavoriteIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(FavoriteIntegrationTestSuite))
}
