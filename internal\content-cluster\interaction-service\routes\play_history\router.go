package play_history

import (
	"github.com/gin-gonic/gin"
	externalHandler "pxpat-backend/internal/content-cluster/interaction-service/external/handler"
	intraHandler "pxpat-backend/internal/content-cluster/interaction-service/intra/handler"
	"pxpat-backend/pkg/auth"
)

// RegisterAllPlayHistoryRoutes 注册所有播放历史相关路由
func RegisterAllPlayHistoryRoutes(
	externalAPI *gin.RouterGroup,
	internalAPI *gin.RouterGroup,
	playHistoryHandler *externalHandler.PlayHistoryHandler,
	internalPlayHistoryHandler *intraHandler.InternalPlayHistoryHandler,
	jwtManager *auth.Manager,
) {
	// 注册外部API路由
	RegisterPlayHistoryExternalRoutes(externalAPI, playHistoryHandler, jwtManager)

	// 注册内部API路由
	RegisterPlayHistoryInternalRoutes(internalAPI, internalPlayHistoryHandler)
}
