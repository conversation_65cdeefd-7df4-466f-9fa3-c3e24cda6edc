package repository

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
)

// FavoriteFolderRepository 收藏夹仓储接口
type FavoriteFolderRepository interface {
	// Create 创建收藏夹
	Create(ctx context.Context, folder *model.FavoriteFolder) error

	// Update 更新收藏夹信息
	Update(ctx context.Context, folder *model.FavoriteFolder) error

	// Delete 删除收藏夹
	Delete(ctx context.Context, folderID string, userKSUID string) error

	// GetByFolderKSUID 根据收藏夹ID获取收藏夹
	GetByFolderKSUID(ctx context.Context, folderID string) (*model.FavoriteFolder, error)

	// GetByUserKSUID 获取用户的所有收藏夹
	GetByUserKSUID(ctx context.Context, userKSUID string) ([]*model.FavoriteFolder, error)

	// GetByUserKSUIDWithPaginationAndPublic 分页获取用户的收藏夹,带is_public状态筛查
	GetByUserKSUIDWithPaginationAndPublic(ctx context.Context, userKSUID string, isPublic bool, page, pageSize int) ([]*model.FavoriteFolder, int64, error)

	// GetDefaultFolder 获取用户的默认收藏夹
	GetDefaultFolder(ctx context.Context, userKSUID string) (*model.FavoriteFolder, error)

	// UpdateItemCount 更新收藏夹的收藏项数量
	UpdateItemCount(ctx context.Context, folderID string, increment bool) error

	// CheckFolderNameExists 检查收藏夹名称是否已存在
	CheckFolderNameExists(ctx context.Context, userKSUID, folderName string, excludeFolderID string) (bool, error)

	// UpdateSortOrder 更新收藏夹排序
	UpdateSortOrder(ctx context.Context, userKSUID string, folderOrders map[string]int) error

	// GetUserFolderStats 获取用户收藏夹统计信息
	GetUserFolderStats(ctx context.Context, userKSUID string) (*dto.GetFavoriteStatsResponse, error)
}

// FavoriteItemRepository 收藏项仓储接口
type FavoriteItemRepository interface {
	// BatchCreate 批量创建收藏项
	BatchCreate(ctx context.Context, items []*model.FavoriteItem) error

	// Delete 删除收藏项
	Delete(ctx context.Context, itemID string, userKSUID string) error

	// DeleteByContentKSUID 根据内容ID删除收藏项
	DeleteByContentKSUID(ctx context.Context, userKSUID, contentKSUID string) error

	// DeleteByFolderID 根据收藏夹ID删除所有收藏项
	DeleteByFolderID(ctx context.Context, userKSUID, folderID string) error

	// GetByID 根据ID获取收藏项
	GetByID(ctx context.Context, itemID string, userKSUID string) (*model.FavoriteItem, error)

	// GetByContentKSUID 根据内容ID获取用户的收藏项
	GetByContentKSUID(ctx context.Context, userKSUID, contentKSUID string) (*model.FavoriteItem, error)

	// GetByUserAndContentKSUID 根据用户和内容ID获取所有收藏项
	GetByUserAndContentKSUID(ctx context.Context, userKSUID, contentKSUID string) ([]*model.FavoriteItem, error)

	// ExistsByUserAndContent 检查用户是否收藏了指定内容
	ExistsByUserAndContent(ctx context.Context, userKSUID, contentKSUID string) (bool, error)

	// GetByUserAndContent 根据用户和内容获取收藏项
	GetByUserAndContent(ctx context.Context, userKSUID, contentKSUID string) ([]*model.FavoriteItem, error)

	// GetUserItemStats 获取用户收藏项统计
	GetUserItemStats(ctx context.Context, userKSUID string) (map[string]int, error)

	// BatchCheckFavoriteStatus 批量检查收藏状态
	BatchCheckFavoriteStatus(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]bool, error)

	// GetByFolderKSUID 获取收藏夹中的收藏项
	GetByFolderKSUID(ctx context.Context, userKSUID, folderID string, page, pageSize int) ([]*model.FavoriteItem, int64, error)

	// GetByFolderAndContentType 按收藏夹和内容类型获取收藏项
	GetByFolderAndContentType(ctx context.Context, userKSUID, folderID string, contentType model.ContentType, page, pageSize int) ([]*model.FavoriteItem, int64, error)

	// GetByUserKSUID 获取用户的所有收藏项
	GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.FavoriteItem, int64, error)

	// GetByUserAndContentType 按用户和内容类型获取收藏项
	GetByUserAndContentType(ctx context.Context, userKSUID string, contentType model.ContentType, page, pageSize int) ([]*model.FavoriteItem, int64, error)

	// MoveTo 移动收藏项到指定收藏夹
	MoveTo(ctx context.Context, itemID, userKSUID, targetFolderID string) error

	// BatchMoveTo 批量移动收藏项
	BatchMoveTo(ctx context.Context, itemIDs []string, userKSUID, targetFolderID string) error

	// GetRecentFavorites 获取用户最近收藏的内容
	GetRecentFavorites(ctx context.Context, userKSUID string, limit int) ([]*model.FavoriteItem, error)

	// SearchFavorites 搜索用户的收藏项
	SearchFavorites(ctx context.Context, userKSUID, keyword string, page, pageSize int) ([]*model.FavoriteItem, int64, error)

	// GetFavoritesByContentKSUIDs 批量获取内容的收藏状态
	GetFavoritesByContentKSUIDs(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]*model.FavoriteItem, error)

	// CountByFolder 统计收藏夹中的收藏项数量
	CountByFolder(ctx context.Context, folderID string) (int64, error)

	// CountByUser 统计用户的收藏项总数
	CountByUser(ctx context.Context, userKSUID string) (int64, error)

	// CountByContentType 按内容类型统计收藏项数量
	CountByContentType(ctx context.Context, userKSUID string, contentType model.ContentType) (int64, error)
}
